{"name": "ueberdosis/tiptap-php", "description": "A PHP package to work with Tiptap output", "keywords": ["ueberd<PERSON>", "tiptap", "prose<PERSON><PERSON>r"], "homepage": "https://github.com/ueberdosis/tiptap-php", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.0", "scrivo/highlight.php": "^9.18", "spatie/shiki-php": "^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.5", "pestphp/pest": "^1.21", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^4.3"}, "autoload": {"psr-4": {"Tiptap\\": "src"}}, "autoload-dev": {"psr-4": {"Tiptap\\Tests\\": "tests"}}, "scripts": {"psalm": "vendor/bin/psalm", "psalm-watch": "nodemon --exec './vendor/bin/psalm || exit 1' --ext php", "test": "./vendor/bin/pest", "test-watch": "nodemon --exec './vendor/bin/pest || exit 1' --ext php", "test-coverage": "./vendor/bin/pest --coverage-html coverage", "format": "vendor/bin/php-cs-fixer fix --allow-risky=yes --config=.php_cs.dist.php"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}